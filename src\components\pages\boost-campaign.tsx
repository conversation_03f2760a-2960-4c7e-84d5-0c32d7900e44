"use client"

import { useState, useEffect } from "react"
import { useNavigate, useParams } from "react-router-dom"
import { ArrowLeft, Zap, CreditCard, DollarSign, Clock, Target } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { dashboardAPI, campaignApi } from "../../lib/api"
import { paymentsApi } from '@/services/api'
import { useToast } from '../../hooks/use-toast'
import { useAuth } from '../../contexts/auth-context'

interface BoostData {
  plan_id: number
  payment_method_id: number
}

interface MoMoFields {
  customer: string
  msisdn: string
  amount: string
  network: string
  narration: string
}

interface Campaign {
  id: number
  title: string
  description: string
  goal_amount: string
  current_amount: string
  image_url: string | null
}

interface BoostPlan {
  id: number
  name: string
  price: string
  duration_days: number
}

interface PaymentMethod {
  id: number
  name: string
  number: string
  type: string
  is_active: number
  created_at: string | null
  updated_at: string | null
}

// Default boost plans - will be replaced by API data
const defaultBoostPlans: BoostPlan[] = [
  {
    id: 1,
    name: "7 days plan",
    price: "100.00",
    duration_days: 7
  },
  {
    id: 2,
    name: "14 days plan",
    price: "200.00",
    duration_days: 14
  },
  {
    id: 3,
    name: "30 days plan",
    price: "400.00",
    duration_days: 30
  }
]

// Default payment methods - will be replaced by API data
const defaultPaymentMethods: PaymentMethod[] = [
  {
    id: 1,
    name: "MOMO",
    number: "**********",
    type: "mobile_money",
    is_active: 1,
    created_at: null,
    updated_at: null
  },
  {
    id: 2,
    name: "Cash/Manual",
    number: "**********",
    type: "bank_transfer",
    is_active: 1,
    created_at: null,
    updated_at: null
  }
]

// Helper function to get proper image URL
const getImageUrl = (url: string | null) => {
  if (!url) return "/placeholder.svg?height=80&width=80";
  if (url.startsWith("http")) return url;
  return `http://127.0.0.1:8000${url}`;
};

// Helper function to format currency as GHS
const formatCurrency = (amount: number | string) => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  return `GHS ${numAmount.toLocaleString()}`;
};

export function BoostCampaignPage() {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const { toast } = useToast()
  const { isAuthenticated, user, loading: authLoading } = useAuth()
  const [loading, setLoading] = useState(false)
  const [campaignLoading, setCampaignLoading] = useState(true)
  const [plansLoading, setPlansLoading] = useState(true)
  const [paymentMethodsLoading, setPaymentMethodsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [campaign, setCampaign] = useState<Campaign | null>(null)
  const [boostPlans, setBoostPlans] = useState<BoostPlan[]>(defaultBoostPlans)
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>(defaultPaymentMethods)
  const [selectedPlan, setSelectedPlan] = useState<BoostPlan | null>(null)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null)
  const [formData, setFormData] = useState<BoostData>({
    plan_id: 0,
    payment_method_id: 0
  })
  const [momoFields, setMomoFields] = useState<MoMoFields>({
    customer: "",
    msisdn: "",
    amount: "",
    network: "",
    narration: ""
  })
  const [paymentStep, setPaymentStep] = useState<'form' | 'processing' | 'checking' | 'boosting'>('form')
  const [timeRemaining, setTimeRemaining] = useState<number>(0)

  // Add authentication logging and checking on component mount
  useEffect(() => {
    console.log('=== BOOST CAMPAIGN PAGE MOUNTED ===')
    console.log('Campaign ID:', id)
    console.log('Environment variables:')
    console.log('- VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL)

    const authToken = localStorage.getItem('authToken')
    console.log('Authentication status:')
    console.log('- Auth context isAuthenticated:', isAuthenticated)
    console.log('- Auth context user:', user)
    console.log('- Auth context loading:', authLoading)
    console.log('- Token exists:', !!authToken)
    console.log('- Token preview:', authToken ? `${authToken.substring(0, 20)}...` : 'null')
    console.log('- Token length:', authToken?.length || 0)

    // Check if token is expired
    if (authToken) {
      try {
        const payload = JSON.parse(atob(authToken.split('.')[1]))
        const isExpired = payload.exp * 1000 < Date.now()
        console.log('- Token expiry:', new Date(payload.exp * 1000))
        console.log('- Token expired:', isExpired)
        console.log('- Current time:', new Date())

        if (isExpired) {
          console.warn('Token is expired, redirecting to login...')
          localStorage.removeItem('authToken')
          navigate('/login')
          return
        }
      } catch (e) {
        console.log('- Token parse error:', e)
        console.warn('Invalid token format, redirecting to login...')
        localStorage.removeItem('authToken')
        navigate('/login')
        return
      }
    }

    // If auth context says not authenticated and not loading, redirect
    if (!authLoading && !isAuthenticated) {
      console.warn('User not authenticated, redirecting to login...')
      navigate('/login')
      return
    }

    // Test API connectivity after authentication check
    if (!authLoading && isAuthenticated) {
      testAPIConnectivity()
    }
  }, [id, isAuthenticated, user, authLoading, navigate])



  // Fetch boost plans from API
  useEffect(() => {
    const fetchBoostPlans = async () => {
      try {
        setPlansLoading(true)
        const response = await dashboardAPI.getBoostPlans()
        console.log('Boost plans response:', response.data)

        // Handle the API response structure
        if (response.data.success && Array.isArray(response.data.data)) {
          setBoostPlans(response.data.data)
        } else {
          console.error('Invalid boost plans response format:', response.data)
        }
      } catch (error: any) {
        console.error("Failed to fetch boost plans:", error)
        // Keep default plans if API fails
      } finally {
        setPlansLoading(false)
      }
    }

    fetchBoostPlans()
  }, [])

  // Fetch payment methods from API
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        setPaymentMethodsLoading(true)
        const response = await dashboardAPI.getPaymentMethods()
        console.log('Payment methods response:', response.data)

        // Handle the API response structure
        if (Array.isArray(response.data.methods)) {
          setPaymentMethods(response.data.methods)
        } else {
          console.error('Invalid payment methods response format:', response.data)
        }
      } catch (error: any) {
        console.error("Failed to fetch payment methods:", error)
        // Keep default payment methods if API fails
      } finally {
        setPaymentMethodsLoading(false)
      }
    }

    fetchPaymentMethods()
  }, [])

  // Network auto-detection for MoMo
  useEffect(() => {
    const msisdn = momoFields.msisdn || "";
    if (
      msisdn.startsWith("024") || msisdn.startsWith("025") || msisdn.startsWith("053") || msisdn.startsWith("054") || msisdn.startsWith("055") || msisdn.startsWith("059") ||
      msisdn.startsWith("+23324") || msisdn.startsWith("+23325") || msisdn.startsWith("+23353") || msisdn.startsWith("+23354") || msisdn.startsWith("+23355") || msisdn.startsWith("+23359")
    ) {
      setMomoFields((prev) => ({ ...prev, network: "MTN" }));
    } else if (
      msisdn.startsWith("020") || msisdn.startsWith("050") ||
      msisdn.startsWith("+23320") || msisdn.startsWith("+23350")
    ) {
      setMomoFields((prev) => ({ ...prev, network: "VODAFONE" }));
    } else if (
      msisdn.startsWith("027") || msisdn.startsWith("057") || msisdn.startsWith("026") ||
      msisdn.startsWith("+23327") || msisdn.startsWith("+23357") || msisdn.startsWith("+23326")
    ) {
      setMomoFields((prev) => ({ ...prev, network: "AIRTELTIGO" }));
    } else {
      setMomoFields((prev) => ({ ...prev, network: "" }));
    }
  }, [momoFields.msisdn])

  // Set amount when plan is selected
  useEffect(() => {
    if (selectedPlan) {
      setMomoFields(prev => ({
        ...prev,
        amount: selectedPlan.price,
        narration: `Boost campaign for ${selectedPlan.duration_days} days`
      }))
    }
  }, [selectedPlan])

  // Fetch campaign details
  useEffect(() => {
    const fetchCampaign = async () => {
      if (!id) return

      try {
        setCampaignLoading(true)
        // Try to fetch campaign from API
        try {
          const response = await campaignApi.getById(parseInt(id))
          if (response.data) {
            setCampaign(response.data)
          } else {
            throw new Error('Campaign not found')
          }
        } catch (apiError) {
          console.warn("API fetch failed, using mock data:", apiError)
          // Fallback to mock campaign data
          setCampaign({
            id: parseInt(id),
            title: "Sample Campaign",
            description: "This is a sample campaign description",
            goal_amount: "10000.00",
            current_amount: "2500.00",
            image_url: null
          })
        }
      } catch (error: any) {
        console.error("Failed to fetch campaign:", error)
        setError("Failed to load campaign details")
        toast({
          title: "Error",
          description: "Failed to load campaign details",
          variant: "destructive",
        })
      } finally {
        setCampaignLoading(false)
      }
    }

    fetchCampaign()
  }, [id, toast])

  // Handle plan selection
  const handlePlanSelect = (planId: string) => {
    const plan = boostPlans.find(p => p.id === parseInt(planId))
    setSelectedPlan(plan || null)
    setFormData(prev => ({ ...prev, plan_id: parseInt(planId) }))
  }

  // Handle payment method selection
  const handlePaymentMethodSelect = (methodId: string) => {
    const method = paymentMethods.find(m => m.id === parseInt(methodId))
    setSelectedPaymentMethod(method || null)
    setFormData(prev => ({ ...prev, payment_method_id: parseInt(methodId) }))
  }

  // Test API connectivity and authentication
  const testAPIConnectivity = async () => {
    console.log('=== TESTING API CONNECTIVITY ===')

    try {
      // Test boost plans API
      console.log('Testing boost plans API...')
      const plansResponse = await dashboardAPI.getBoostPlans()
      console.log('Boost plans API response:', plansResponse.status, plansResponse.data)
    } catch (error: any) {
      console.error('Boost plans API failed:', error.response?.status, error.response?.data)
    }

    try {
      // Test payment methods API
      console.log('Testing payment methods API...')
      const methodsResponse = await dashboardAPI.getPaymentMethods()
      console.log('Payment methods API response:', methodsResponse.status, methodsResponse.data)
    } catch (error: any) {
      console.error('Payment methods API failed:', error.response?.status, error.response?.data)
    }

    try {
      // Test a simple authenticated endpoint
      console.log('Testing dashboard stats API...')
      const statsResponse = await dashboardAPI.getDashboardStats()
      console.log('Dashboard stats API response:', statsResponse.status, statsResponse.data)
    } catch (error: any) {
      console.error('Dashboard stats API failed:', error.response?.status, error.response?.data)
      if (error.response?.status === 401) {
        console.error('Authentication failed - user will be redirected to login')
      }
    }
  }

  // Handle form submission with multi-step API flow
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    // Add comprehensive logging
    console.log('=== BOOST CAMPAIGN SUBMISSION STARTED ===')
    console.log('Form data:', formData)
    console.log('Selected plan:', selectedPlan)
    console.log('Selected payment method:', selectedPaymentMethod)
    console.log('MoMo fields:', momoFields)

    // Check authentication status
    const authToken = localStorage.getItem('authToken')
    console.log('Auth token exists:', !!authToken)
    console.log('Auth token preview:', authToken ? `${authToken.substring(0, 20)}...` : 'null')

    // Validation
    if (!formData.plan_id) {
      console.error('Validation failed: No plan selected')
      setError('Please select a boost plan')
      return
    }
    if (!formData.payment_method_id) {
      console.error('Validation failed: No payment method selected')
      setError('Please select a payment method')
      return
    }

    // Additional validation for MoMo payment
    if (selectedPaymentMethod?.type === 'mobile_money') {
      if (!momoFields.customer.trim()) {
        console.error('Validation failed: No customer name')
        setError('Please enter customer name')
        return
      }
      if (!momoFields.msisdn.trim()) {
        console.error('Validation failed: No mobile number')
        setError('Please enter mobile number')
        return
      }
      if (!momoFields.network) {
        console.error('Validation failed: Network not detected')
        setError('Network could not be detected. Please check your mobile number')
        return
      }
    }

    console.log('All validations passed, proceeding with submission...')

    try {
      setLoading(true)
      console.log('=== STARTING PAYMENT PROCESS ===')

      // Step 1: For MoMo payment, initiate debit wallet and start status checking
      if (selectedPaymentMethod?.type === 'mobile_money') {
        setPaymentStep('processing')
        console.log('Step 1: Initiating wallet debit with MoMo data:', momoFields)
        console.log('Payment API base URL:', import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000')

        // Start the debit wallet process to get transaction ID
        let transactionId: string | null = null

        // Initiate debit wallet and get transaction ID
        try {
          console.log('Step 1: Calling paymentsApi.debitWallet...')
          console.log('Request payload:', momoFields)

          const debitResponse = await paymentsApi.debitWallet(momoFields)
          console.log('Debit wallet response status:', debitResponse.status)
          console.log('Debit wallet response headers:', debitResponse.headers)
          console.log('Debit wallet response data:', debitResponse.data)

          // Extract transaction ID from response structure: { errorCode, error, data: { transactionId } }
          if (debitResponse.data.errorCode === "000" && debitResponse.data.data) {
            transactionId = debitResponse.data.data.transactionId
          }

          if (!transactionId) {
            console.error('No transaction ID found in debit response:', debitResponse.data)
            const errorMsg = debitResponse.data.error || 'Payment reference number not received. Please try again.'
            throw new Error(errorMsg)
          }

          console.log('Payment transaction ID:', transactionId)

        } catch (error: any) {
          console.error('Debit wallet failed:', error)

          // Cache MoMo error in localStorage for debugging
          const momoError = {
            timestamp: new Date().toISOString(),
            error: error.response?.data?.message || error.message || 'Failed to initiate payment',
            errorData: error.response?.data,
            momoFields: momoFields,
            step: 'debit_wallet'
          }
          localStorage.setItem('lastMoMoError', JSON.stringify(momoError))
          console.log('MoMo debit error cached to localStorage:', momoError)

          // Check for specific MoMo failure
          const errorData = error.response?.data
          if (errorData?.errorCode === "100" && errorData?.error === "Transaction Failed") {
            throw Object.assign(new Error('Specific MoMo transaction failure detected'), {
              isSpecificMoMoFailure: true,
              errorData: errorData
            })
          }

          throw Object.assign(new Error(error.response?.data?.message || error.message || 'Failed to initiate payment. Please try again.'), {
            errorData: errorData
          })
        }

        // Start checking payment status immediately
        setPaymentStep('checking')
        let statusCheckAttempts = 0
        const maxAttempts = 10 // Check for up to 10 times (30 seconds)
        const checkInterval = 3000 // Check every 3 seconds
        setTimeRemaining(30) // Start countdown from 30 seconds

        console.log('Step 2: Starting payment status monitoring...')

        // Show initiation toast
        toast({
          title: "Payment Initiated",
          description: "Please complete the payment on your mobile device",
          variant: "default",
        })

        const checkStatus = async (): Promise<boolean> => {
          try {
            // Add a small delay before first check to allow debit to process
            if (statusCheckAttempts === 0) {
              await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds before first check
            }

            const statusResponse = await paymentsApi.checkStatus(transactionId!)
            console.log(`Payment status check (${statusCheckAttempts + 1}/${maxAttempts}) for transaction ${transactionId}:`, statusResponse.data)

            // Show status update toast
            toast({
              title: "Checking Payment Status",
              description: `Attempt ${statusCheckAttempts + 1} of ${maxAttempts}`,
              variant: "default",
            })

            // Check for success - based on Donatepage pattern
            const status = statusResponse.data.status

            if (status === 'success' || status === 'completed' || status === 'approved') {
              console.log('Payment verified successfully!')
              toast({
                title: "Payment Successful",
                description: "Your payment has been verified successfully",
                variant: "default",
              })
              return true
            }

            // Check for explicit failure
            if (status === 'failed' || status === 'error' || status === 'declined' || status === 'cancelled') {
              throw new Error(statusResponse.data.message || 'Payment failed. Please try again.')
            }

            // If status is still 'pending', continue checking
            console.log(`Payment status: ${status}, continuing to check...`)

            statusCheckAttempts++
            const remaining = Math.max(0, 30 - (statusCheckAttempts * 3))
            setTimeRemaining(remaining)

            if (statusCheckAttempts >= maxAttempts) {
              setTimeRemaining(0)
              throw new Error('Payment verification timeout (30 seconds). Please check your mobile money and try again if payment was successful.')
            }

            // Wait before next check
            await new Promise(resolve => setTimeout(resolve, checkInterval))
            return await checkStatus()

          } catch (error: any) {
            console.error('Status check error:', error)

            // If it's a timeout or explicit failure error, throw it as is
            if (error.message.includes('timeout') || error.message.includes('failed')) {
              throw error
            }

            // For network/API errors, continue checking if we haven't exceeded max attempts
            statusCheckAttempts++
            const remaining = Math.max(0, 30 - (statusCheckAttempts * 3))
            setTimeRemaining(remaining)

            if (statusCheckAttempts >= maxAttempts) {
              setTimeRemaining(0)
              throw new Error('Payment verification timeout. Please check your mobile money and try again if payment was successful.')
            }

            console.log(`Network error on attempt ${statusCheckAttempts}, continuing to check...`)
            // Continue checking for network errors
            await new Promise(resolve => setTimeout(resolve, checkInterval))
            return await checkStatus()
          }
        }

        // Check payment status (this will run for up to 30 seconds)
        const paymentSuccessful = await checkStatus()

        if (!paymentSuccessful) {
          throw new Error('Payment was not successful. Please try again.')
        }

        console.log('Payment process completed successfully!')
      }

      // Step 3: Boost the campaign
      setPaymentStep('boosting')
      console.log('=== STEP 3: BOOSTING CAMPAIGN ===')
      console.log('Boost data:', formData)
      console.log('Campaign ID:', id)

      // Check auth token again before boost API call
      const authTokenBeforeBoost = localStorage.getItem('authToken')
      console.log('Auth token before boost API call:', !!authTokenBeforeBoost)

      const campaignId = parseInt(id || '0')
      console.log('Calling dashboardAPI.boostCampaign with:', { campaignId, formData })
      console.log('API endpoint will be:', `/api/v1/boost-campaign/${campaignId}`)
      console.log('Base URL:', import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8000")

      let boostResponse: any
      try {
        boostResponse = await dashboardAPI.boostCampaign(campaignId, formData)
        console.log('Boost response status:', boostResponse.status)
        console.log('Boost response headers:', boostResponse.headers)
        console.log('Campaign boosted successfully:', boostResponse.data)
      } catch (boostError: any) {
        console.error('=== BOOST API CALL FAILED ===')
        console.error('Boost error:', boostError)
        console.error('Boost error response:', boostError.response)
        console.error('Boost error status:', boostError.response?.status)
        console.error('Boost error data:', boostError.response?.data)
        console.error('Boost error config:', boostError.config)
        throw boostError // Re-throw to be caught by outer catch
      }

      if (boostResponse.data.success) {
        const successMessage = boostResponse.data.message || "Campaign boosted successfully!"
        setSuccess(successMessage)
        setPaymentStep('form')
        setTimeRemaining(0)

        // Show success toast
        toast({
          title: "Success!",
          description: successMessage,
          variant: "default",
        })

        // Redirect after a short delay to show success message
        setTimeout(() => {
          navigate(`/campaigns/${id}`)
        }, 3000)
      } else {
        throw new Error("Failed to boost campaign. Please try again.")
      }

    } catch (error: any) {
      console.error('=== BOOST PROCESS FAILED ===')
      console.error('Error object:', error)
      console.error('Error message:', error.message)
      console.error('Error response:', error.response)
      console.error('Error response status:', error.response?.status)
      console.error('Error response data:', error.response?.data)
      console.error('Error response headers:', error.response?.headers)
      console.error('Error config:', error.config)
      console.error('Error stack:', error.stack)

      setPaymentStep('form')
      setTimeRemaining(0)

      // Handle different types of errors
      let errorMessage = 'Failed to process boost. Please try again.'
      let isSpecificMoMoFailure = false

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (error.response?.data?.errors) {
        const errors = error.response.data.errors
        errorMessage = Object.keys(errors).map(key => `${key}: ${errors[key].join(', ')}`).join('\n')
      } else if (error.message) {
        errorMessage = error.message
      }

      // Check for specific MoMo failure (errorCode: 100, error: "Transaction Failed")
      if (selectedPaymentMethod?.type === 'mobile_money') {
        // Check if this is the specific MoMo failure from the error object
        if (error.isSpecificMoMoFailure || (error.errorData?.errorCode === "100" && error.errorData?.error === "Transaction Failed")) {
          isSpecificMoMoFailure = true
          console.log('Detected specific MoMo failure - not proceeding with guest boost')
        }

        // Cache MoMo error in localStorage for debugging (if not already cached)
        if (!localStorage.getItem('lastMoMoError') || !error.errorData) {
          const momoError = {
            timestamp: new Date().toISOString(),
            error: errorMessage,
            errorData: error.response?.data || error.errorData,
            momoFields: momoFields,
            transactionId: error.transactionId || 'unknown',
            step: 'main_process'
          }
          localStorage.setItem('lastMoMoError', JSON.stringify(momoError))
          console.log('MoMo error cached to localStorage:', momoError)
        }
      }

      // If payment failed but it's not a specific MoMo failure, record boost as guest
      if (!isSpecificMoMoFailure && selectedPaymentMethod?.type === 'mobile_money') {
        try {
          console.log('Payment failed but not specific MoMo failure - attempting guest boost...')

          toast({
            title: "Processing Guest Boost",
            description: "Payment failed, but recording boost as guest...",
            variant: "default",
          })

          const campaignId = parseInt(id || '0')
          const guestBoostResponse = await dashboardAPI.boostCampaign(campaignId, formData)

          if (guestBoostResponse.data.success) {
            const successMessage = "Boost recorded as guest due to payment issues. Please contact support if payment was deducted."
            setSuccess(successMessage)

            toast({
              title: "Guest Boost Recorded",
              description: successMessage,
              variant: "default",
            })

            // Redirect after showing success
            setTimeout(() => {
              navigate(`/campaigns/${id}`)
            }, 3000)

            return // Exit early since guest boost was successful
          }
        } catch (guestError: any) {
          console.error('Guest boost also failed:', guestError)
          errorMessage += '\n\nAdditionally, guest boost recording failed. Please contact support.'
        }
      }

      setError(errorMessage)

      // Show error toast
      toast({
        title: "Boost Failed",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Show loading while auth is being checked
  if (authLoading) {
    return (
      <div className="space-y-6 px-4 md:px-6 lg:px-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => navigate('/campaigns')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Boost Campaign</h2>
            <p className="text-muted-foreground">Checking authentication...</p>
          </div>
        </div>
        <div className="flex justify-center">
          <Card className="w-full max-w-2xl animate-pulse">
            <CardHeader>
              <div className="h-6 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-full"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="h-48 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (campaignLoading) {
    return (
      <div className="space-y-6 px-4 md:px-6 lg:px-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => navigate('/campaigns')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Boost Campaign</h2>
            <p className="text-muted-foreground">Loading campaign details...</p>
          </div>
        </div>
        <div className="flex justify-center">
          <Card className="w-full max-w-2xl animate-pulse">
            <CardHeader>
              <div className="h-6 bg-muted rounded w-3/4"></div>
              <div className="h-4 bg-muted rounded w-full"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="h-48 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (!campaign) {
    return (
      <div className="space-y-6 px-4 md:px-6 lg:px-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => navigate('/campaigns')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h2 className="text-3xl font-bold tracking-tight">Boost Campaign</h2>
            <p className="text-muted-foreground">Campaign not found</p>
          </div>
        </div>
        <div className="flex justify-center">
          <Card className="w-full max-w-2xl">
            <CardContent className="pt-6">
              <div className="text-center text-red-600">
                <p>Campaign not found or failed to load.</p>
                <Button 
                  variant="outline" 
                  className="mt-4" 
                  onClick={() => navigate('/campaigns')}
                >
                  Back to Campaigns
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6 px-4 md:px-6 lg:px-8">
      <div className="flex items-center justify-between">
        <Button variant="ghost" size="icon" onClick={() => navigate('/campaigns')}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="text-center flex-1">
          <h2 className="text-3xl font-bold tracking-tight">Boost Campaign</h2>
          <p className="text-muted-foreground">Increase your campaign's visibility</p>
        </div>
        <div className="w-10"></div>
      </div>

      <div className="flex justify-center">
        <div className="w-full max-w-4xl space-y-6">
          {/* Campaign Preview */}
          <Card className="shadow-lg border-0" style={{ boxShadow: '0 10px 25px -5px rgba(55, 183, 255, 0.1), 0 8px 10px -6px rgba(55, 183, 255, 0.1)' }}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-[#37b7ff]" />
                Campaign to Boost
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="relative">
                  <img
                    src={getImageUrl(campaign.image_url)}
                    alt={campaign.title}
                    className="w-full h-48 object-cover rounded-lg"
                    onError={(e) => {
                      console.log('Image failed to load:', campaign.image_url)
                      e.currentTarget.src = '/placeholder.svg?height=400&width=600'
                    }}
                  />
                </div>
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold">{campaign.title}</h3>
                  <p className="text-muted-foreground">{campaign.description}</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Progress</span>
                      <span>{Math.round((parseFloat(campaign.current_amount) / parseFloat(campaign.goal_amount)) * 100)}%</span>
                    </div>
                    <div className="h-2 rounded-full bg-muted">
                      <div
                        className="h-2 rounded-full bg-[#37b7ff]"
                        style={{ width: `${Math.min((parseFloat(campaign.current_amount) / parseFloat(campaign.goal_amount)) * 100, 100)}%` }}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm">
                        <DollarSign className="mr-1 h-4 w-4" />
                        {formatCurrency(campaign.current_amount)} raised
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Goal: {formatCurrency(campaign.goal_amount)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Boost Form */}
          <Card className="shadow-lg border-0" style={{ boxShadow: '0 10px 25px -5px rgba(255, 215, 0, 0.1), 0 8px 10px -6px rgba(255, 215, 0, 0.1)' }}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                Select Boost Plan
              </CardTitle>
              <CardDescription>Choose a plan to boost your campaign's visibility</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="rounded-lg bg-red-50 border border-red-200 p-3 text-sm text-red-700 whitespace-pre-line">
                    {error}
                  </div>
                )}

                {success && (
                  <div className="rounded-lg bg-green-50 border border-green-200 p-3 text-sm text-green-700">
                    {success}
                  </div>
                )}

                {/* Payment Processing Status */}
                {paymentStep === 'processing' && (
                  <div className="rounded-lg bg-yellow-50 border border-yellow-200 p-4 text-sm text-yellow-700">
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
                      <div>
                        <div className="font-semibold">Initiating Payment...</div>
                        <div className="text-xs mt-1">Setting up your mobile money payment</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Payment Verification Countdown */}
                {paymentStep === 'checking' && timeRemaining > 0 && (
                  <div className="rounded-lg bg-blue-50 border border-blue-200 p-4 text-sm text-blue-700">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold">Verifying Payment...</div>
                        <div className="text-xs mt-1">Please complete the payment on your mobile device. You have up to 30 seconds.</div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold">{timeRemaining}s</div>
                        <div className="text-xs">Time remaining</div>
                      </div>
                    </div>
                    <div className="mt-2 w-full bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${(timeRemaining / 30) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}

                {/* Campaign Boosting Status */}
                {paymentStep === 'boosting' && (
                  <div className="rounded-lg bg-green-50 border border-green-200 p-4 text-sm text-green-700">
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                      <div>
                        <div className="font-semibold">Boosting Campaign...</div>
                        <div className="text-xs mt-1">Payment successful! Applying boost to your campaign</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Boost Plans */}
                <div className="space-y-4">
                  <Label>Choose Boost Plan *</Label>
                  <div className="grid md:grid-cols-3 gap-4">
                    {boostPlans.map((plan) => (
                      <Card 
                        key={plan.id} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedPlan?.id === plan.id 
                            ? 'ring-2 ring-yellow-500 bg-yellow-50' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handlePlanSelect(plan.id.toString())}
                      >
                        <CardHeader className="pb-3">
                          <CardTitle className="text-lg">{plan.name}</CardTitle>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Clock className="h-4 w-4" />
                            {plan.duration_days} days
                          </div>
                          <div className="text-2xl font-bold text-yellow-600">
                            {formatCurrency(plan.price)}
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="space-y-2 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-yellow-500"></div>
                              Featured placement
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-yellow-500"></div>
                              Priority in search results
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-yellow-500"></div>
                              Enhanced visibility for {plan.duration_days} days
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Payment Method */}
                <div className="space-y-2">
                  <Label htmlFor="payment_method">Payment Method *</Label>
                  <Select 
                    value={formData.payment_method_id.toString()} 
                    onValueChange={handlePaymentMethodSelect}
                  >
                    <SelectTrigger className="h-12">
                      <SelectValue placeholder="Select payment method" />
                    </SelectTrigger>
                    <SelectContent>
                      {paymentMethods.map((method) => (
                        <SelectItem key={method.id} value={method.id.toString()}>
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4" />
                            {method.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* MoMo Payment Fields */}
                {selectedPaymentMethod?.type === 'mobile_money' && (
                  <div className="space-y-4 p-4 border rounded-lg bg-blue-50">
                    <h4 className="font-semibold text-blue-800">Mobile Money Payment Details</h4>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="customer">Customer Name *</Label>
                        <Input
                          id="customer"
                          type="text"
                          placeholder="Enter your full name"
                          value={momoFields.customer}
                          onChange={(e) => setMomoFields(prev => ({ ...prev, customer: e.target.value }))}
                          className="h-12"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="msisdn">Mobile Number *</Label>
                        <Input
                          id="msisdn"
                          type="tel"
                          placeholder="e.g., **********"
                          value={momoFields.msisdn}
                          onChange={(e) => setMomoFields(prev => ({ ...prev, msisdn: e.target.value }))}
                          className="h-12"
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="network">Network</Label>
                        <Input
                          id="network"
                          type="text"
                          value={momoFields.network}
                          readOnly
                          className="h-12 bg-gray-100"
                          placeholder="Auto-detected"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="amount">Amount (GHS)</Label>
                        <Input
                          id="amount"
                          type="text"
                          value={momoFields.amount}
                          readOnly
                          className="h-12 bg-gray-100"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="narration">Narration</Label>
                      <Input
                        id="narration"
                        type="text"
                        value={momoFields.narration}
                        onChange={(e) => setMomoFields(prev => ({ ...prev, narration: e.target.value }))}
                        className="h-12"
                        placeholder="Payment description"
                      />
                    </div>
                  </div>
                )}

                {/* Summary */}
                {selectedPlan && (
                  <Card className="bg-yellow-50 border-yellow-200">
                    <CardContent className="pt-6">
                      <h4 className="font-semibold mb-2">Boost Summary</h4>
                      <div className="space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span>Plan:</span>
                          <span>{selectedPlan.name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Duration:</span>
                          <span>{selectedPlan.duration_days} days</span>
                        </div>
                        <div className="flex justify-between font-semibold">
                          <span>Total:</span>
                          <span>{formatCurrency(selectedPlan.price)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Debug Button */}
                <div className="flex gap-2 pt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={testAPIConnectivity}
                    className="text-xs"
                  >
                    Test API Connectivity
                  </Button>
                </div>

                {/* Submit Button */}
                <div className="flex gap-4 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/campaigns')}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading || !selectedPlan || parseFloat(selectedPlan?.price || '0') === 0}
                    className="flex-1 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white font-semibold shadow-lg transform transition-all hover:scale-[1.02]"
                  >
                    <Zap className="mr-2 h-4 w-4" />
                    {loading ? (
                      paymentStep === 'processing' ? "Processing Payment..." :
                      paymentStep === 'checking' ? `Verifying Payment... (${timeRemaining}s)` :
                      paymentStep === 'boosting' ? "Boosting Campaign..." :
                      "Processing..."
                    ) : `Boost for ${formatCurrency(selectedPlan?.price || '0')}`}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
