"use client"

import { useEffect, useState } from "react"
import { <PERSON>et, DollarSign, Clock, CheckCircle, XCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

const withdrawals = [
  {
    id: 1,
    amount: 1500,
    date: "2024-01-15",
    status: "completed",
    method: "Bank Transfer",
    campaignTitle: "Help Build Clean Water Wells",
    fee: 15,
  },
  {
    id: 2,
    amount: 750,
    date: "2024-01-12",
    status: "pending",
    method: "PayPal",
    campaignTitle: "Education for All Children",
    fee: 7.5,
  },
  {
    id: 3,
    amount: 2000,
    date: "2024-01-08",
    status: "failed",
    method: "Bank Transfer",
    campaignTitle: "Emergency Medical Fund",
    fee: 20,
  },
]

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-4 w-4 text-green-600" />
    case "pending":
      return <Clock className="h-4 w-4 text-orange-600" />
    case "failed":
      return <XCircle className="h-4 w-4 text-red-600" />
    default:
      return null
  }
}

const getStatusVariant = (status: string) => {
  switch (status) {
    case "completed":
      return "default"
    case "pending":
      return "secondary"
    case "failed":
      return "destructive"
    default:
      return "secondary"
  }
}

export function WithdrawalsPage() {
  const [withdrawalList, setWithdrawalList] = useState(withdrawals)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const fetchWithdrawals = async () => {
      try {
        setLoading(true)
        // Uncomment when you have a real API
        // const response = await dashboardAPI.getWithdrawals()
        // setWithdrawalList(response.data)
      } catch (error) {
        console.error("Failed to fetch withdrawals:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchWithdrawals()
  }, [])

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Withdrawals</h2>
          <p className="text-muted-foreground">Manage your campaign fund withdrawals</p>
        </div>
        <Button>
          <Wallet className="mr-2 h-4 w-4" />
          Request Withdrawal
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Withdrawn</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$1,500</div>
            <p className="text-xs text-muted-foreground">Successfully withdrawn</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$750</div>
            <p className="text-xs text-muted-foreground">Processing</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Balance</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">GHS 3,250</div>
            <p className="text-xs text-muted-foreground">Ready to withdraw</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Fees</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">GHS 42.50</div>
            <p className="text-xs text-muted-foreground">Processing fees</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Withdrawal History</CardTitle>
          <CardDescription>Track your withdrawal requests and their status</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Fee</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {withdrawalList.map((withdrawal) => (
                <TableRow key={withdrawal.id}>
                  <TableCell className="font-medium">{withdrawal.campaignTitle}</TableCell>
                  <TableCell>${withdrawal.amount.toLocaleString()}</TableCell>
                  <TableCell>{withdrawal.method}</TableCell>
                  <TableCell>${withdrawal.fee}</TableCell>
                  <TableCell>{new Date(withdrawal.date).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(withdrawal.status)}
                      <Badge variant={getStatusVariant(withdrawal.status) as any}>{withdrawal.status}</Badge>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
